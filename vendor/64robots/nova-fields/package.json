{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs"}, "devDependencies": {"babel-plugin-component": "^1.1.1", "cross-env": "^5.0.0", "element-ui": "^2.8.2", "flatpickr": "^4.6.1", "form-backend-validation": "^2.3.3", "laravel-mix": "^3.0.0", "webpack": "3.12.0", "laravel-nova": "^1.7.0", "trix": "^0.12.1", "vue-clickaway": "^2.2.2", "vue-multiselect": "^2.1.6", "vuepress": "^1.1.0"}, "dependencies": {"vue": "^2.6.11"}}