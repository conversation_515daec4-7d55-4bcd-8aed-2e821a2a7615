{"name": "babenkoivan/elastic-migrations", "description": "Elasticsearch migrations for Laravel", "keywords": ["laravel", "migrations", "elastic", "elasticsearch", "php"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "funding": [{"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/ivanbabenko"}, {"type": "paypal", "url": "https://paypal.me/babenkoi"}], "autoload": {"psr-4": {"ElasticMigrations\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"ElasticMigrations\\Tests\\": "tests"}}, "require": {"php": "^7.2 || ^8.0", "babenkoivan/elastic-client": "^1.2", "babenkoivan/elastic-adapter": "^1.14"}, "require-dev": {"phpunit/phpunit": "^9.5", "orchestra/testbench": "^6.12", "friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.32"}, "config": {"bin-dir": "bin"}, "extra": {"laravel": {"providers": ["ElasticMigrations\\ServiceProvider"]}}}