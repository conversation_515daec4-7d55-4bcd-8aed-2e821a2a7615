{"name": "babe<PERSON><PERSON>n/elastic-scout-driver", "description": "Elasticsearch driver for Laravel Scout", "keywords": ["scout", "elastic", "elasticsearch", "laravel", "driver", "php"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "funding": [{"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/ivanbabenko"}, {"type": "paypal", "url": "https://paypal.me/babenkoi"}], "autoload": {"psr-4": {"ElasticScoutDriver\\": "src"}}, "autoload-dev": {"psr-4": {"ElasticScoutDriver\\Tests\\": "tests"}}, "require": {"php": "^7.2 || ^8.0", "babenkoivan/elastic-client": "^1.2", "babenkoivan/elastic-adapter": "^1.13"}, "require-dev": {"phpunit/phpunit": "^9.5", "orchestra/testbench": "^6.12", "babenkoivan/elastic-migrations": "^1.4", "laravel/scout": "^9.0", "friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^0.12.32", "laravel/legacy-factories": "^1.1"}, "config": {"bin-dir": "bin"}, "extra": {"laravel": {"providers": ["ElasticScoutDriver\\ServiceProvider"]}}}