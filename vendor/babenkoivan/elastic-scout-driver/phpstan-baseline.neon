parameters:
	ignoreErrors:
		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 3
			path: src/Engine.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getScoutKey\\(\\)\\.$#"
			count: 1
			path: src/Factories/DocumentFactory.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:pushSoftDeleteMetadata\\(\\)\\.$#"
			count: 1
			path: src/Factories/DocumentFactory.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:scoutMetadata\\(\\)\\.$#"
			count: 1
			path: src/Factories/DocumentFactory.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:toSearchableArray\\(\\)\\.$#"
			count: 1
			path: src/Factories/DocumentFactory.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getScoutKey\\(\\)\\.$#"
			count: 4
			path: src/Factories/ModelFactory.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getScoutModelsByIds\\(\\)\\.$#"
			count: 1
			path: src/Factories/ModelFactory.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:queryScoutModelsByIds\\(\\)\\.$#"
			count: 1
			path: src/Factories/ModelFactory.php

		-
			message: "#^Cannot call method run\\(\\) on Illuminate\\\\Testing\\\\PendingCommand\\|int\\.$#"
			count: 4
			path: tests/Integration/TestCase.php

