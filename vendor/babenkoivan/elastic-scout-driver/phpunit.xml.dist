<?xml version="1.0" encoding="UTF-8"?>
<phpunit
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd"
        bootstrap="vendor/autoload.php"
        executionOrder="depends,defects"
        forceCoversAnnotation="true"
        beStrictAboutCoversAnnotation="true"
        beStrictAboutOutputDuringTests="true"
        beStrictAboutTodoAnnotatedTests="true"
        verbose="true"
        colors="true">
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <exclude>
            <file>src/ServiceProvider.php</file>
        </exclude>
    </coverage>
    <testsuites>
        <testsuite name="integration">
            <directory suffix="Test.php">tests/Integration</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_CONNECTION" value="mysql"/>
        <env name="DB_HOST" value="127.0.0.1"/>
        <env name="DB_PORT" value="13306"/>
        <env name="DB_DATABASE" value="test"/>
        <env name="DB_USERNAME" value="test"/>
        <env name="DB_PASSWORD" value="test"/>
        <env name="ELASTIC_HOST" value="127.0.0.1:19200"/>
    </php>
</phpunit>
