name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-18.04
    strategy:
      matrix:
        php: [7.2, 7.3, 7.4, 8.0]
        include:
          - php: 7.2
            phpunit: 8.5

          - php: 7.3
            phpunit: 9.5

          - php: 7.4
            phpunit: 9.5

          - php: 8.0
            phpunit: 9.5
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install php and composer
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          coverage: none
          tools: composer:v2

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Restore composer cache
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: composer require --no-interaction phpunit/phpunit:^${{ matrix.phpunit }}

      - name: Run tests
        run: make test

