{"name": "babenkoivan/elastic-adapter", "description": "Adapter for official PHP Elasticsearch client", "keywords": ["elastic", "elasticsearch", "adapter", "client", "php"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "funding": [{"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/ivanbabenko"}, {"type": "paypal", "url": "https://paypal.me/babenkoi"}], "autoload": {"psr-4": {"ElasticAdapter\\": "src"}}, "autoload-dev": {"psr-4": {"ElasticAdapter\\Tests\\": "tests"}}, "require": {"php": "^7.2 || ^8.0", "elasticsearch/elasticsearch": "^7.3"}, "require-dev": {"phpunit/phpunit": "^9.5", "friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.32"}, "config": {"bin-dir": "bin"}}