{"name": "babenkoivan/elastic-client", "description": "The official PHP Elasticsearch client integrated with Laravel", "keywords": ["laravel", "elastic", "elasticsearch", "client", "php"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "funding": [{"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/ivanbabenko"}, {"type": "paypal", "url": "https://paypal.me/babenkoi"}], "autoload": {"psr-4": {"ElasticClient\\": "src"}}, "autoload-dev": {"psr-4": {"ElasticClient\\Tests\\": "tests"}}, "require": {"php": "^7.2 || ^8.0", "elasticsearch/elasticsearch": "^7.3"}, "require-dev": {"phpunit/phpunit": "^9.5", "orchestra/testbench": "^6.12", "friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.32"}, "config": {"bin-dir": "bin"}, "extra": {"laravel": {"providers": ["ElasticClient\\ServiceProvider"]}}}