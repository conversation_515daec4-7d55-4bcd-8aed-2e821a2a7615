{"name": "asm89/stack-cors", "description": "Cross-origin resource sharing library and stack middleware", "keywords": ["stack", "cors"], "homepage": "https://github.com/asm89/stack-cors", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.0|^8.0", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "squizlabs/php_codesniffer": "^3.5"}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "autoload-dev": {"psr-4": {"Asm89\\Stack\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "check-style": "phpcs -p --standard=PSR12 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src", "fix-style": "phpcbf -p --standard=PSR12 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src"}, "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}