name: cwn-api
recipe: laravel
config:
  php: 7.4
  cache: redis
  webroot: public
services:
  appserver:
    extras:
      - dpkg -i /app/wkhtmltox_0.12.6-1.stretch_amd64.deb
  elasticsearch:
    type: elasticsearch:7
    portforward: true
    plugins: []
  pma:
    type: phpmyadmin
    hosts:
      - database
  database:
    type: mariadb
  mailhog:
    type: mailhog:v1.0.0
    portforward: false
    hogfrom: []
proxy:
  mailhog:
    - mail.cwn-api.lndo.site
  pma:
    - db.cwn-api.lndo.site